[package]
name = "colony-app"
version = "1.1.6"
description = "A GUI frontend to the Autonomi network with a client side search engine"
authors = ["<PERSON>lish", "<PERSON><PERSON>"]
edition = "2021"
homepage = "https://github.com/zettawatt/colony"
license = "GPL-3.0-only"

[[bin]]
name = "colony-app"
path = "src/main.rs"

[lib]
name = "colony_app"
crate-type = ["staticlib", "cdylib", "rlib"]

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2", features = [] }
ureq = { version = "2", features = ["json"] }
serde_json = "1"

[dependencies]
tauri = { version = "2", features = ["rustls-tls"] }
tauri-plugin-opener = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
tauri-plugin-notification = "2"
#colonylib = "0.5.15"
colonylib = {path = "../../colonylib"}
autonomi = "0.5.3"
tracing = "0.1.41"
tokio = { version = "1", features = ["full"] }
chrono = { version = "0.4", features = ["serde"] }
tracing-subscriber = "0.3.18"
thiserror = "2.0.12"
tauri-plugin-dialog = "2"
tauri-plugin-store = "2"
tauri-plugin-shell = "2"
clap = "4.5.40"
tauri-plugin-log = "2"
reqwest = { version = "0.12", features = ["json", "rustls-tls"], default-features = false }
anttp = "0.12.0"

[profile.dev]
opt-level = 0
incremental = true

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true

[dev-dependencies]
tokio-test = "0.4"
ruint = "1.12.3"
tracing-subscriber = "0.3.18"
futures = "0.3"

[target.aarch64-apple-darwin.env]
SDKROOT = "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk"
CPLUS_INCLUDE_PATH = "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1"

[target.x86_64-apple-darwin.env]
SDKROOT = "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk"
CPLUS_INCLUDE_PATH = "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1"
